This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.5.8)  3 AUG 2025 12:19
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**paper.tex
(./paper.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(d:/texlive/2025/texmf-dist/tex/latex/ieeetran/IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen141
\@IEEEtrantmpdimenB=\dimen142
\@IEEEtrantmpdimenC=\dimen143
\@IEEEtrantmpcountA=\count196
\@IEEEtrantmpcountB=\count197
\@IEEEtrantmpcountC=\count198
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 5
03.
(d:/texlive/2025/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen144
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen145
\CLASSINFOnormalsizeunitybaselineskip=\dimen146
\IEEEnormaljot=\dimen147
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <5> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <7> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <8> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <9> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <10> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <11> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <11> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <12> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <17> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <17> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <20> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <20> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <24> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <24> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

\IEEEquantizedlength=\dimen148
\IEEEquantizedlengthdiff=\dimen149
\IEEEquantizedtextheightdiff=\dimen150
\IEEEilabelindentA=\dimen151
\IEEEilabelindentB=\dimen152
\IEEEilabelindent=\dimen153
\IEEEelabelindent=\dimen154
\IEEEdlabelindent=\dimen155
\IEEElabelindent=\dimen156
\IEEEiednormlabelsep=\dimen157
\IEEEiedmathlabelsep=\dimen158
\IEEEiedtopsep=\skip49
\c@section=\count199
\c@subsection=\count266
\c@subsubsection=\count267
\c@paragraph=\count268
\c@IEEEsubequation=\count269
\abovecaptionskip=\skip50
\belowcaptionskip=\skip51
\c@figure=\count270
\c@table=\count271
\@IEEEeqnnumcols=\count272
\@IEEEeqncolcnt=\count273
\@IEEEsubeqnnumrollback=\count274
\@IEEEquantizeheightA=\dimen159
\@IEEEquantizeheightB=\dimen160
\@IEEEquantizeheightC=\dimen161
\@IEEEquantizeprevdepth=\dimen162
\@IEEEquantizemultiple=\count275
\@IEEEquantizeboxA=\box52
\@IEEEtmpitemindent=\dimen163
\IEEEPARstartletwidth=\dimen164
\c@IEEEbiography=\count276
\@IEEEtranrubishbin=\box53
)
** ATTENTION: Overriding command lockouts (line 2).
(d:/texlive/2025/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
)
(d:/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip52

For additional information on amsmath, use the `?' option.
(d:/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(d:/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen165
))
(d:/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen166
)
(d:/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count277
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count278
\leftroot@=\count279
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count280
\DOTSCASE@=\count281
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box54
\strutbox@=\box55
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen167
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count282
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count283
\dotsspace@=\muskip17
\c@parentequation=\count284
\dspbrk@lvl=\count285
\tag@help=\toks19
\row@=\count286
\column@=\count287
\maxfields@=\count288
\andhelp@=\toks20
\eqnshift@=\dimen168
\alignsep@=\dimen169
\tagshift@=\dimen170
\tagwidth@=\dimen171
\totwidth@=\dimen172
\lineht@=\dimen173
\@envbody=\toks21
\multlinegap=\skip53
\multlinetaggap=\skip54
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(d:/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(d:/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(d:/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(d:/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks23
)
(d:/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(d:/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(d:/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(d:/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen174
\Gin@req@width=\dimen175
)
(d:/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)
(d:/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(d:/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(d:/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(d:/texlive/2025/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating enviro
nment

(d:/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count289
\float@exts=\toks24
\float@box=\box56
\@float@everytoks=\toks25
\@floatcapt=\box57
)
(d:/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks26
\c@algorithm=\count290
)
(d:/texlive/2025/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 

(d:/texlive/2025/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count291
\c@ALG@rem=\count292
\c@ALG@nested=\count293
\ALG@tlm=\skip55
\ALG@thistlm=\skip56
\c@ALG@Lnr=\count294
\c@ALG@blocknr=\count295
\c@ALG@storecount=\count296
\c@ALG@tmpcounter=\count297
\ALG@tmplength=\skip57
)
Document Style - pseudocode environments for use with the `algorithmicx' style
) (d:/texlive/2025/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip58
\multirow@cntb=\count298
\multirow@dima=\skip59
\bigstrutjot=\dimen176
)
(d:/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen177
\lightrulewidth=\dimen178
\cmidrulewidth=\dimen179
\belowrulesep=\dimen180
\belowbottomsep=\dimen181
\aboverulesep=\dimen182
\abovetopsep=\dimen183
\cmidrulesep=\dimen184
\cmidrulekern=\dimen185
\defaultaddspace=\dimen186
\@cmidla=\count299
\@cmidlb=\count300
\@aboverulesep=\dimen187
\@belowrulesep=\dimen188
\@thisruleclass=\count301
\@lastruleclass=\count302
\@thisrulewidth=\dimen189
)
(d:/texlive/2025/texmf-dist/tex/latex/subfigure/subfigure.sty
Package: subfigure 2002/03/15 v2.1.5 subfigure package
\subfigtopskip=\skip60
\subfigcapskip=\skip61
\subfigcaptopadj=\dimen190
\subfigbottomskip=\skip62
\subfigcapmargin=\dimen191
\subfiglabelskip=\skip63
\c@subfigure=\count303
\c@lofdepth=\count304
\c@subtable=\count305
\c@lotdepth=\count306

****************************************
* Local config file subfigure.cfg used *
****************************************
(d:/texlive/2025/texmf-dist/tex/latex/subfigure/subfigure.cfg)
\subfig@top=\skip64
\subfig@bottom=\skip65
)
(d:/texlive/2025/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2025/02/11 v3.2a Micro-typographical refinements (RS)

(d:/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count307
)
\MT@toks=\toks27
\MT@tempbox=\box58
\MT@count=\count308
LaTeX Info: Redefining \noprotrusionifhmode on input line 1087.
LaTeX Info: Redefining \leftprotrusion on input line 1088.
\MT@prot@toks=\toks28
LaTeX Info: Redefining \rightprotrusion on input line 1107.
LaTeX Info: Redefining \textls on input line 1449.
\MT@outer@kern=\dimen192
LaTeX Info: Redefining \microtypecontext on input line 2053.
LaTeX Info: Redefining \textmicrotypecontext on input line 2070.
\MT@listname@count=\count309

(d:/texlive/2025/texmf-dist/tex/latex/microtype/microtype-pdftex.def
File: microtype-pdftex.def 2025/02/11 v3.2a Definitions specific to pdftex (RS)

LaTeX Info: Redefining \lsstyle on input line 944.
LaTeX Info: Redefining \lslig on input line 944.
\MT@outer@space=\skip66
)
Package microtype Info: Loading configuration file microtype.cfg.

(d:/texlive/2025/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2025/02/11 v3.2a microtype main configuration file (RS)
)
LaTeX Info: Redefining \microtypesetup on input line 3065.
)
(d:/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count310
\l__pdf_internal_box=\box59
)
(./paper.aux)
\openout1 = `paper.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 15.
LaTeX Font Info:    ... okay on input line 15.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 15.
LaTeX Font Info:    ... okay on input line 15.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 15.
LaTeX Font Info:    ... okay on input line 15.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 15.
LaTeX Font Info:    ... okay on input line 15.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 15.
LaTeX Font Info:    ... okay on input line 15.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 15.
LaTeX Font Info:    ... okay on input line 15.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 15.
LaTeX Font Info:    ... okay on input line 15.

-- Lines per column: 56 (exact).
(d:/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count311
\scratchdimen=\dimen193
\scratchbox=\box60
\nofMPsegments=\count312
\nofMParguments=\count313
\everyMPshowfont=\toks29
\MPscratchCnt=\count314
\MPscratchDim=\dimen194
\MPnumerator=\count315
\makeMPintoPDFobject=\count316
\everyMPtoPDFconversion=\toks30
) (d:/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(d:/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
LaTeX Info: Redefining \microtypecontext on input line 15.
Package microtype Info: Applying patch `item' on input line 15.
Package microtype Info: Applying patch `toc' on input line 15.
Package microtype Info: Applying patch `eqnum' on input line 15.
Package microtype Info: Applying patch `footnote' on input line 15.
Package microtype Info: Applying patch `verbatim' on input line 15.
LaTeX Info: Redefining \microtypesetup on input line 15.
Package microtype Info: Generating PDF output.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using default protrusion set `alltext'.
Package microtype Info: Automatic font expansion enabled (level 2),
(microtype)             stretch: 20, shrink: 20, step: 1, non-selected.
Package microtype Info: Using default expansion set `alltext-nott'.
LaTeX Info: Redefining \showhyphens on input line 15.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of interword spacing.
Package microtype Info: No adjustment of character kerning.

(d:/texlive/2025/texmf-dist/tex/latex/microtype/mt-ptm.cfg
File: mt-ptm.cfg 2006/04/20 v1.7 microtype config. file: Times (RS)
)



[1{d:/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{d:/texlive/202
5/texmf-dist/fonts/enc/dvips/base/8r.enc}


]
(d:/texlive/2025/texmf-dist/tex/latex/microtype/mt-cmr.cfg
File: mt-cmr.cfg 2013/05/19 v2.2 microtype config. file: Computer Modern Roman 
(RS)
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 70.


(d:/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
(d:/texlive/2025/texmf-dist/tex/latex/microtype/mt-msa.cfg
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 70.


(d:/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
(d:/texlive/2025/texmf-dist/tex/latex/microtype/mt-msb.cfg
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
)



[2]



[3]



[4]
Underfull \hbox (badness 3343) in paragraph at lines 260--261
[]\OT1/ptm/m/n/10 (+20) Gen-er-ate per-son-al-ized global mod-els $\OMS/cmsy/m/
n/10 f[][]g$ \OT1/ptm/m/n/10 (+20) via
 []





[5]



[6]
Underfull \hbox (badness 7613) in paragraph at lines 414--415
[]\OT1/ptm/m/n/10 (+20) The to-tal com-plex-ity is $\OML/cmm/m/it/10 O\OT1/cmr/
m/n/10 (\OML/cmm/m/it/10 K \OMS/cmsy/m/n/10 ^^A \OML/cmm/m/it/10 B \OMS/cmsy/m/
n/10 ^^A \OML/cmm/m/it/10 D \OT1/cmr/m/n/10 + \OML/cmm/m/it/10 K[] \OMS/cmsy/m/
n/10 ^^A
 []




Underfull \hbox (badness 5681) in paragraph at lines 427--428
[]\OT1/ptm/m/it/10 (+20) Multimodal Func-tions: \OT1/ptm/m/n/10 (+20) F4-F12 (R
osen-brock, Ack-
 []



[7]
Overfull \hbox (31.69357pt too wide) in paragraph at lines 470--481
 [][] 
 []





[8]
Underfull \hbox (badness 10000) in paragraph at lines 547--548
[]\OT1/ptm/m/it/10 (+20) Budget Al-lo-ca-tion Sen-si-tiv-ity: \OT1/ptm/m/n/10 (
+20) Eval-u-at-ing dif-fer-ent
 []


Underfull \hbox (badness 2837) in paragraph at lines 557--560
[]\OT1/ptm/b/n/10 (+20) Similarity Mea-sure Com-par-i-son: \OT1/ptm/m/n/10 (+20
) We com-pare the
 []





[9]

No file paper.bbl.

** Conference Paper **
Before submitting the final camera ready copy, remember to:

 1. Manually equalize the lengths of two columns on the last page
 of your paper;

 2. Ensure that any PostScript and/or PDF output post-processing
 uses only Type 1 fonts and that every step in the generation
 process uses the appropriate paper size.



[10] (./paper.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
 ) 
Here is how much of TeX's memory you used:
 7747 strings out of 475118
 120698 string characters out of 5765021
 566911 words of memory out of 5000000
 30316 multiletter control sequences out of 15000+600000
 615365 words of font info for 334 fonts, out of 8000000 for 9000
 36 hyphenation exceptions out of 8191
 57i,8n,65p,1451b,315s stack positions out of 10000i,1000n,20000p,200000b,200000s
<d:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb><d:/tex
live/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx7.pfb><d:/texlive/2025/
texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><d:/texlive/2025/texmf-dis
t/fonts/type1/public/amsfonts/cmextra/cmex7.pfb><d:/texlive/2025/texmf-dist/fon
ts/type1/public/amsfonts/cm/cmmi10.pfb><d:/texlive/2025/texmf-dist/fonts/type1/
public/amsfonts/cm/cmmi5.pfb><d:/texlive/2025/texmf-dist/fonts/type1/public/ams
fonts/cm/cmmi7.pfb><d:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/c
mmib10.pfb><d:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb
><d:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr5.pfb><d:/texliv
e/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb><d:/texlive/2025/texm
f-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><d:/texlive/2025/texmf-dist/fo
nts/type1/public/amsfonts/cm/cmsy5.pfb><d:/texlive/2025/texmf-dist/fonts/type1/
public/amsfonts/cm/cmsy7.pfb><d:/texlive/2025/texmf-dist/fonts/type1/public/ams
fonts/symbols/msbm10.pfb><d:/texlive/2025/texmf-dist/fonts/type1/urw/times/utmb
8a.pfb><d:/texlive/2025/texmf-dist/fonts/type1/urw/times/utmbi8a.pfb><d:/texliv
e/2025/texmf-dist/fonts/type1/urw/times/utmr8a.pfb><d:/texlive/2025/texmf-dist/
fonts/type1/urw/times/utmri8a.pfb>
Output written on paper.pdf (10 pages, 261758 bytes).
PDF statistics:
 133 PDF objects out of 1000 (max. 8388607)
 82 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 54273 words of extra memory for PDF output out of 61914 (max. 10000000)

